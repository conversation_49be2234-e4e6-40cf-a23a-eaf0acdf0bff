@extends('layouts.template')

@section('title', 'Paramètres - CRFM')

@section('page-header')
@section('page-title', 'Paramètres du système')
@section('page-description', 'Configuration et paramétrage de l'application')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Paramètres</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Paramètres généraux -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-settings text-c-blue me-2"></i>Paramètres généraux</h5>
            </div>
            <div class="card-block">
                <form>
                    <div class="form-group">
                        <label>Nom de l'organisme</label>
                        <input type="text" class="form-control" value="CRFM - Caisse de Retraite des Fonctionnaires du Mayotte">
                    </div>
                    <div class="form-group">
                        <label>Adresse</label>
                        <textarea class="form-control" rows="3">Mamoudzou, Mayotte</textarea>
                    </div>
                    <div class="form-group">
                        <label>Email de contact</label>
                        <input type="email" class="form-control" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Téléphone</label>
                        <input type="tel" class="form-control" value="+262 20 22 XX XX">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="feather icon-save me-2"></i>Enregistrer
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Paramètres de calcul -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-calculator text-c-green me-2"></i>Paramètres de calcul</h5>
            </div>
            <div class="card-block">
                <form>
                    <div class="form-group">
                        <label>Taux de cotisation (%)</label>
                        <input type="number" class="form-control" value="18" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>Âge de départ à la retraite</label>
                        <input type="number" class="form-control" value="60">
                    </div>
                    <div class="form-group">
                        <label>Durée minimale de cotisation (années)</label>
                        <input type="number" class="form-control" value="15">
                    </div>
                    <div class="form-group">
                        <label>Taux de liquidation maximum (%)</label>
                        <input type="number" class="form-control" value="75" step="0.1">
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="feather icon-save me-2"></i>Enregistrer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Gestion des utilisateurs -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-users text-c-purple me-2"></i>Gestion des utilisateurs</h5>
                <div class="card-header-right">
                    <button class="btn btn-primary btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouvel utilisateur
                    </button>
                </div>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Rôle</th>
                                <th>Dernière connexion</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Admin CRFM</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">Administrateur</span></td>
                                <td>Aujourd'hui 14:30</td>
                                <td><span class="badge bg-success">Actif</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info"><i class="feather icon-edit"></i></button>
                                    <button class="btn btn-sm btn-warning"><i class="feather icon-lock"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>Gestionnaire 1</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-primary">Gestionnaire</span></td>
                                <td>Hier 16:45</td>
                                <td><span class="badge bg-success">Actif</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info"><i class="feather icon-edit"></i></button>
                                    <button class="btn btn-sm btn-danger"><i class="feather icon-trash"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Sauvegarde et maintenance -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-database text-c-yellow me-2"></i>Sauvegarde et maintenance</h5>
            </div>
            <div class="card-block">
                <div class="alert alert-info">
                    <i class="feather icon-info me-2"></i>
                    Dernière sauvegarde : Aujourd'hui à 02:00
                </div>
                <div class="row">
                    <div class="col-6">
                        <button class="btn btn-warning btn-block">
                            <i class="feather icon-download me-2"></i>Sauvegarder
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-info btn-block">
                            <i class="feather icon-upload me-2"></i>Restaurer
                        </button>
                    </div>
                </div>
                <hr>
                <h6>Maintenance programmée</h6>
                <div class="form-group">
                    <label>Heure de sauvegarde automatique</label>
                    <input type="time" class="form-control" value="02:00">
                </div>
                <button class="btn btn-primary btn-sm">Programmer</button>
            </div>
        </div>
    </div>
    
    <!-- Logs et monitoring -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-activity text-c-red me-2"></i>Logs et monitoring</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-green">99.8%</h4>
                            <p class="text-muted">Disponibilité</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-blue">1.2s</h4>
                            <p class="text-muted">Temps de réponse</p>
                        </div>
                    </div>
                </div>
                <hr>
                <h6>Logs récents</h6>
                <div class="log-container" style="max-height: 200px; overflow-y: auto;">
                    <small class="text-success">✓ 14:30 - <NAME_EMAIL></small><br>
                    <small class="text-info">ℹ 14:25 - Sauvegarde automatique terminée</small><br>
                    <small class="text-warning">⚠ 14:20 - Tentative de connexion échouée</small><br>
                    <small class="text-success">✓ 14:15 - Nouveau dossier créé PL2024006</small><br>
                    <small class="text-info">ℹ 14:10 - Mise à jour système effectuée</small><br>
                </div>
                <div class="m-t-10">
                    <button class="btn btn-outline-primary btn-sm">Voir tous les logs</button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
