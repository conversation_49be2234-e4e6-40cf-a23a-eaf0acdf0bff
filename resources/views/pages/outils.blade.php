@extends('layouts.template')

@section('title', 'Outils - CRFM')

@section('page-header')
@section('page-title', 'Outils et utilitaires')
@section('page-description', 'Outils d'aide à la gestion et calculateurs')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Outils</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Calculateur de pension -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-calculator text-c-blue me-2"></i>Calculateur de pension</h5>
            </div>
            <div class="card-block">
                <form id="pensionCalculator">
                    <div class="form-group">
                        <label>Salaire de référence (€)</label>
                        <input type="number" class="form-control" id="salaireRef" placeholder="Ex: 1800" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label>Durée de cotisation (années)</label>
                        <input type="number" class="form-control" id="dureeCotisation" placeholder="Ex: 25" step="0.25" min="0">
                    </div>
                    <div class="form-group">
                        <label>Type de pension</label>
                        <select class="form-control" id="typePension">
                            <option value="vieillesse">Vieillesse</option>
                            <option value="anticipee">Anticipée</option>
                            <option value="invalidite">Invalidité</option>
                            <option value="survivant">Survivant</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Âge de départ (optionnel)</label>
                        <input type="number" class="form-control" id="ageDepart" placeholder="Ex: 60" min="50" max="70">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="calculerPension()" id="btnCalculer">
                        <i class="feather icon-play me-2"></i>Calculer
                    </button>
                </form>
                <div id="resultatCalcul" class="m-t-20" style="display: none;">
                    <div class="alert alert-success">
                        <h6>Résultat du calcul CRFM :</h6>
                        <div class="row">
                            <div class="col-6">
                                <p class="m-b-5"><strong>Pension brute :</strong> <span id="montantBrut"></span> €</p>
                                <p class="m-b-5"><strong>Pension nette :</strong> <span id="montantNet"></span> €</p>
                            </div>
                            <div class="col-6">
                                <p class="m-b-5"><strong>Taux :</strong> <span id="tauxLiquidation"></span>%</p>
                                <p class="m-b-5"><strong>Coefficient :</strong> <span id="coefficient"></span></p>
                            </div>
                        </div>
                        <hr>
                        <small class="text-muted" id="detailsCalcul"></small>
                    </div>
                </div>
                <div id="erreurCalcul" class="m-t-20" style="display: none;">
                    <div class="alert alert-danger">
                        <h6>Erreur :</h6>
                        <p class="m-b-0" id="messageErreur"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Générateur de numéros -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-hash text-c-green me-2"></i>Générateur de numéros</h5>
            </div>
            <div class="card-block">
                <div class="form-group">
                    <label>Type de numéro</label>
                    <select class="form-control" id="typeNumero">
                        <option value="affiliation">Affiliation (AFF)</option>
                        <option value="declaration">Déclaration (DECL)</option>
                        <option value="preliquidation">Pré-liquidation (PL)</option>
                        <option value="adherent">Adhérent (ADH)</option>
                        <option value="cotisation">Cotisation (COT)</option>
                    </select>
                </div>
                <button type="button" class="btn btn-success" onclick="genererNumero()" id="btnGenerer">
                    <i class="feather icon-refresh-cw me-2"></i>Générer
                </button>
                <div id="numeroGenere" class="m-t-20" style="display: none;">
                    <div class="alert alert-info">
                        <h6>Numéro généré :</h6>
                        <h4 id="nouveauNumero" class="text-c-blue"></h4>
                        <button class="btn btn-sm btn-outline-primary" onclick="copierNumero()">
                            <i class="feather icon-copy me-1"></i>Copier
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Import/Export -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-upload text-c-purple me-2"></i>Import/Export de données</h5>
            </div>
            <div class="card-block">
                <h6>Import de données</h6>
                <div class="form-group">
                    <label>Type de données</label>
                    <select class="form-control">
                        <option>Adhérents</option>
                        <option>Cotisations</option>
                        <option>Employeurs</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Fichier Excel/CSV</label>
                    <input type="file" class="form-control" accept=".xlsx,.xls,.csv">
                </div>
                <button class="btn btn-primary">
                    <i class="feather icon-upload me-2"></i>Importer
                </button>
                
                <hr>
                
                <h6>Export de données</h6>
                <div class="row">
                    <div class="col-6">
                        <button class="btn btn-success btn-block">
                            <i class="feather icon-download me-2"></i>Export Excel
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-info btn-block">
                            <i class="feather icon-file-text me-2"></i>Export PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vérificateur de données -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-check-circle text-c-yellow me-2"></i>Vérificateur de données</h5>
            </div>
            <div class="card-block">
                <div class="alert alert-info">
                    <i class="feather icon-info me-2"></i>
                    Dernière vérification : {{ $statistiquesVerification['derniere_verification'] }}
                </div>

                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-green">{{ $statistiquesVerification['adherents_valides'] }}</h4>
                            <p class="text-muted">Adhérents valides</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-blue">{{ $statistiquesVerification['cotisations_coherentes'] }}</h4>
                            <p class="text-muted">Cotisations cohérentes</p>
                        </div>
                    </div>
                </div>
                <div class="row m-t-15">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-purple">{{ $statistiquesVerification['dossiers_complets'] }}</h4>
                            <p class="text-muted">Dossiers complets</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-yellow">{{ $statistiquesVerification['declarations_conformes'] }}</h4>
                            <p class="text-muted">Déclarations conformes</p>
                        </div>
                    </div>
                </div>

                <div class="alert alert-{{ $statistiquesVerification['statut_global'] === 'Bon' ? 'success' : ($statistiquesVerification['statut_global'] === 'Moyen' ? 'warning' : 'danger') }} m-t-15">
                    <strong>Statut global :</strong> {{ $statistiquesVerification['statut_global'] }}
                </div>

                <button class="btn btn-warning btn-block" onclick="lancerVerification()" id="btnVerifier">
                    <i class="feather icon-search me-2"></i>Lancer vérification
                </button>
                <div id="resultatVerification" class="m-t-20" style="display: none;"></div>
                
                <div class="m-t-20">
                    <h6>Contrôles disponibles :</h6>
                    <div class="checkbox-fade fade-in-primary">
                        <label>
                            <input type="checkbox" checked>
                            <span class="cr"><i class="cr-icon icofont icofont-ui-check txt-primary"></i></span>
                            <span>Doublons adhérents</span>
                        </label>
                    </div>
                    <div class="checkbox-fade fade-in-primary">
                        <label>
                            <input type="checkbox" checked>
                            <span class="cr"><i class="cr-icon icofont icofont-ui-check txt-primary"></i></span>
                            <span>Cohérence cotisations</span>
                        </label>
                    </div>
                    <div class="checkbox-fade fade-in-primary">
                        <label>
                            <input type="checkbox" checked>
                            <span class="cr"><i class="cr-icon icofont icofont-ui-check txt-primary"></i></span>
                            <span>Validité des calculs</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Calendrier des échéances -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-calendar text-c-red me-2"></i>Calendrier des échéances</h5>
                <div class="card-header-right">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active">Mois</button>
                        <button class="btn btn-outline-primary">Semaine</button>
                        <button class="btn btn-outline-primary">Jour</button>
                    </div>
                </div>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Priorité</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tachesRecentes as $tache)
                            <tr>
                                <td>{{ $tache['echeance'] }}</td>
                                <td><span class="badge bg-primary">Système</span></td>
                                <td>{{ $tache['titre'] }}</td>
                                <td>
                                    <span class="badge bg-{{ $tache['priorite'] === 'Haute' ? 'danger' : ($tache['priorite'] === 'Moyenne' ? 'warning' : 'info') }}">
                                        {{ $tache['priorite'] }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $tache['statut'] === 'En cours' ? 'warning' : ($tache['statut'] === 'Planifié' ? 'info' : 'secondary') }}">
                                        {{ $tache['statut'] }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary"><i class="feather icon-eye"></i></button>
                                    @if($tache['statut'] === 'En cours')
                                        <button class="btn btn-sm btn-success"><i class="feather icon-check"></i></button>
                                    @else
                                        <button class="btn btn-sm btn-warning"><i class="feather icon-clock"></i></button>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function calculerPension() {
    const salaire = parseFloat(document.getElementById('salaireRef').value);
    const duree = parseFloat(document.getElementById('dureeCotisation').value);
    const type = document.getElementById('typePension').value;
    const age = document.getElementById('ageDepart').value;

    if (!salaire || !duree) {
        afficherErreur('Veuillez remplir au minimum le salaire de référence et la durée de cotisation');
        return;
    }

    // Désactiver le bouton pendant le calcul
    const btn = document.getElementById('btnCalculer');
    btn.disabled = true;
    btn.innerHTML = '<i class="feather icon-loader me-2"></i>Calcul...';

    // Appel API
    fetch('{{ route("outils.api.calculer-pension") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            salaire_reference: salaire,
            duree_cotisation: duree,
            type_pension: type,
            age_depart: age || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            afficherResultat(data.data);
        } else {
            afficherErreur('Erreur lors du calcul');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        afficherErreur('Erreur de communication avec le serveur');
    })
    .finally(() => {
        // Réactiver le bouton
        btn.disabled = false;
        btn.innerHTML = '<i class="feather icon-play me-2"></i>Calculer';
    });
}

function afficherResultat(data) {
    document.getElementById('montantBrut').textContent = data.montant_brut.toLocaleString('fr-FR');
    document.getElementById('montantNet').textContent = data.montant_net.toLocaleString('fr-FR');
    document.getElementById('tauxLiquidation').textContent = data.taux_liquidation;
    document.getElementById('coefficient').textContent = data.coefficient_minoration;
    document.getElementById('detailsCalcul').textContent = data.details;

    document.getElementById('erreurCalcul').style.display = 'none';
    document.getElementById('resultatCalcul').style.display = 'block';
}

function afficherErreur(message) {
    document.getElementById('messageErreur').textContent = message;
    document.getElementById('resultatCalcul').style.display = 'none';
    document.getElementById('erreurCalcul').style.display = 'block';
}

function genererNumero() {
    const type = document.getElementById('typeNumero').value;

    // Désactiver le bouton pendant la génération
    const btn = document.getElementById('btnGenerer');
    btn.disabled = true;
    btn.innerHTML = '<i class="feather icon-loader me-2"></i>Génération...';

    // Appel API
    fetch('{{ route("outils.api.generer-numero") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('nouveauNumero').textContent = data.numero;
            document.getElementById('numeroGenere').style.display = 'block';
        } else {
            alert('Erreur lors de la génération du numéro');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de communication avec le serveur');
    })
    .finally(() => {
        // Réactiver le bouton
        btn.disabled = false;
        btn.innerHTML = '<i class="feather icon-refresh-cw me-2"></i>Générer';
    });
}

function copierNumero() {
    const numero = document.getElementById('nouveauNumero').textContent;
    navigator.clipboard.writeText(numero).then(() => {
        alert('Numéro copié dans le presse-papiers !');
    });
}

function lancerVerification() {
    const btn = document.getElementById('btnVerifier');
    btn.disabled = true;
    btn.innerHTML = '<i class="feather icon-loader me-2"></i>Vérification...';

    fetch('{{ route("outils.api.verifier-donnees") }}', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            afficherResultatVerification(data.data);
        } else {
            alert('Erreur lors de la vérification');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de communication avec le serveur');
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = '<i class="feather icon-search me-2"></i>Lancer vérification';
    });
}

function afficherResultatVerification(data) {
    let html = '<div class="alert alert-info"><h6>Résultats de la vérification :</h6>';

    // Adhérents
    html += `<p><strong>Adhérents :</strong> ${data.adherents.total} total, `;
    html += `${data.adherents.sans_numero} sans numéro, `;
    html += `${data.adherents.doublons} doublons `;
    html += `<span class="badge bg-${data.adherents.statut === 'OK' ? 'success' : 'warning'}">${data.adherents.statut}</span></p>`;

    // Cotisations
    html += `<p><strong>Cotisations :</strong> ${data.cotisations.total} total, `;
    html += `${data.cotisations.sans_montant} sans montant, `;
    html += `${data.cotisations.orphelines} orphelines `;
    html += `<span class="badge bg-${data.cotisations.statut === 'OK' ? 'success' : 'warning'}">${data.cotisations.statut}</span></p>`;

    // Pré-liquidations
    html += `<p><strong>Pré-liquidations :</strong> ${data.preliquidations.total} total, `;
    html += `${data.preliquidations.inconsistants} inconsistants `;
    html += `<span class="badge bg-${data.preliquidations.statut === 'OK' ? 'success' : 'warning'}">${data.preliquidations.statut}</span></p>`;

    html += '</div>';

    document.getElementById('resultatVerification').innerHTML = html;
    document.getElementById('resultatVerification').style.display = 'block';
}
</script>
@endpush
