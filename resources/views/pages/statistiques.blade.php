@extends('layouts.template')

@section('title', 'Statistiques - CRFM')

@section('page-header')
@section('page-title', 'Statistiques et Rapports')
@section('page-description', 'Analyse des données et indicateurs de performance')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Statistiques</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- KPI Cards avec données réelles -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($kpis['total_affilies']) }}</h4>
                        <h6 class="text-white m-b-0">Total Affiliés</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-users f-28"></i>
                    </div>
                </div>
                <div class="m-t-10">
                    @if($kpis['evolution_affilies'] >= 0)
                        <span class="badge bg-light text-primary">+{{ $kpis['evolution_affilies'] }}% ce mois</span>
                    @else
                        <span class="badge bg-light text-danger">{{ $kpis['evolution_affilies'] }}% ce mois</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($kpis['cotisations_montant'] / 1000, 1) }}K</h4>
                        <h6 class="text-white m-b-0">Cotisations (€)</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-trending-up f-28"></i>
                    </div>
                </div>
                <div class="m-t-10">
                    @if($kpis['evolution_cotisations'] >= 0)
                        <span class="badge bg-light text-success">+{{ $kpis['evolution_cotisations'] }}% ce mois</span>
                    @else
                        <span class="badge bg-light text-danger">{{ $kpis['evolution_cotisations'] }}% ce mois</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($kpis['retraites_count']) }}</h4>
                        <h6 class="text-white m-b-0">Retraités</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-award f-28"></i>
                    </div>
                </div>
                <div class="m-t-10">
                    @if($kpis['evolution_retraites'] >= 0)
                        <span class="badge bg-light text-warning">+{{ $kpis['evolution_retraites'] }}% ce mois</span>
                    @else
                        <span class="badge bg-light text-danger">{{ $kpis['evolution_retraites'] }}% ce mois</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($kpis['pensions_montant'] / 1000, 1) }}K</h4>
                        <h6 class="text-white m-b-0">Pensions (€)</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-credit-card f-28 text-white"></i>
                    </div>
                </div>
                <div class="m-t-10">
                    @if($kpis['evolution_pensions'] >= 0)
                        <span class="badge bg-light text-success">+{{ $kpis['evolution_pensions'] }}% ce mois</span>
                    @else
                        <span class="badge bg-light text-danger">{{ $kpis['evolution_pensions'] }}% ce mois</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Graphique évolution -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bar-chart text-c-blue me-2"></i>Évolution des cotisations et pensions</h5>
                <div class="card-header-right">
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active">12 mois</button>
                        <button class="btn btn-outline-primary">6 mois</button>
                        <button class="btn btn-outline-primary">3 mois</button>
                    </div>
                </div>
            </div>
            <div class="card-block">
                <canvas id="evolutionChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Répartition par type -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-pie-chart text-c-green me-2"></i>Répartition des pensions</h5>
            </div>
            <div class="card-block">
                <!-- Graphique Chart.js (caché par défaut) -->
                <canvas id="repartitionChart" height="300" style="display: none;"></canvas>

                <!-- Graphique CSS (affiché par défaut) -->
                <div id="repartitionFallback" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center">
                                <!-- Graphique CSS simple -->
                                <div style="position: relative; width: 200px; height: 200px; margin: 0 auto;">
                                    <div style="
                                        width: 200px;
                                        height: 200px;
                                        border-radius: 50%;
                                        background: conic-gradient(
                                            #1f77b4 0deg 240deg,
                                            #2ca02c 240deg 360deg
                                        );
                                        position: relative;
                                    ">
                                        <div style="
                                            position: absolute;
                                            top: 50%;
                                            left: 50%;
                                            transform: translate(-50%, -50%);
                                            background: white;
                                            width: 80px;
                                            height: 80px;
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            font-weight: bold;
                                            color: #333;
                                        ">
                                            6<br><small>Total</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="m-t-30">
                                <div class="d-flex align-items-center m-b-15">
                                    <div style="width: 16px; height: 16px; background: #1f77b4; border-radius: 50%; margin-right: 10px;"></div>
                                    <span><strong>Vieillesse:</strong> 4 dossiers (66.7%)</span>
                                </div>
                                <div class="d-flex align-items-center m-b-15">
                                    <div style="width: 16px; height: 16px; background: #2ca02c; border-radius: 50%; margin-right: 10px;"></div>
                                    <span><strong>Anticipée:</strong> 2 dossiers (33.3%)</span>
                                </div>
                                <div class="d-flex align-items-center m-b-15">
                                    <div style="width: 16px; height: 16px; background: #ff7f0e; border-radius: 50%; margin-right: 10px;"></div>
                                    <span><strong>Invalidité:</strong> 0 dossier (0%)</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div style="width: 16px; height: 16px; background: #d62728; border-radius: 50%; margin-right: 10px;"></div>
                                    <span><strong>Survivant:</strong> 0 dossier (0%)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="m-t-20">
                    <div class="row">
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-c-blue" style="width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></div>
                                <span class="text-muted">Vieillesse ({{ $repartitionData['percentages'][0] }}%)</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-c-green" style="width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></div>
                                <span class="text-muted">Anticipée ({{ $repartitionData['percentages'][1] }}%)</span>
                            </div>
                        </div>
                    </div>
                    <div class="row m-t-10">
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-c-yellow" style="width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></div>
                                <span class="text-muted">Invalidité ({{ $repartitionData['percentages'][2] }}%)</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-c-purple" style="width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></div>
                                <span class="text-muted">Survivant ({{ $repartitionData['percentages'][3] }}%)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top employeurs -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-building text-c-blue me-2"></i>Top 5 Employeurs</h5>
            </div>
            <div class="card-block">
                @foreach($topEmployeurs as $index => $employeur)
                    <div class="row align-items-center {{ $loop->last ? '' : 'm-b-20' }}">
                        <div class="col-8">
                            <h6 class="m-b-5">{{ $employeur['nom'] }}</h6>
                            <p class="text-muted m-b-0">{{ number_format($employeur['employes']) }} employés</p>
                        </div>
                        <div class="col-4 text-end">
                            <h6 class="m-b-0 {{ $index === 0 ? 'text-c-blue' : ($index === 1 ? 'text-c-green' : ($index === 2 ? 'text-c-yellow' : ($index === 3 ? 'text-c-purple' : 'text-muted'))) }}">
                                {{ $employeur['cotisations'] }}K €
                            </h6>
                        </div>
                    </div>
                    @if(!$loop->last)
                        <div class="progress m-b-20" style="height: 6px;">
                            <div class="progress-bar {{ $index === 0 ? 'bg-c-blue' : ($index === 1 ? 'bg-c-green' : ($index === 2 ? 'bg-c-yellow' : ($index === 3 ? 'bg-c-purple' : 'bg-secondary'))) }}"
                                 style="width: {{ $employeur['pourcentage'] }}%"></div>
                        </div>
                    @else
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-secondary" style="width: {{ $employeur['pourcentage'] }}%"></div>
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
    
    <!-- Indicateurs de performance -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-target text-c-green me-2"></i>Indicateurs de performance</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-blue">{{ $indicateurs['taux_recouvrement'] }}%</h4>
                            <p class="text-muted m-b-0">Taux de recouvrement</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-green">{{ $indicateurs['delai_traitement'] }}j</h4>
                            <p class="text-muted m-b-0">Délai moyen traitement</p>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-yellow">{{ $indicateurs['satisfaction_clients'] }}%</h4>
                            <p class="text-muted m-b-0">Satisfaction clients</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h4 class="text-c-purple">{{ $indicateurs['dossiers_traites'] }}</h4>
                            <p class="text-muted m-b-0">Dossiers traités/mois</p>
                        </div>
                    </div>
                </div>
                <hr>
                @if($indicateurs['taux_recouvrement'] >= 90 && $indicateurs['delai_traitement'] <= 20)
                    <div class="alert alert-success">
                        <i class="feather icon-trending-up me-2"></i>
                        <strong>Excellente performance !</strong> Tous les indicateurs sont au vert ce mois-ci.
                    </div>
                @elseif($indicateurs['taux_recouvrement'] >= 80)
                    <div class="alert alert-warning">
                        <i class="feather icon-alert-triangle me-2"></i>
                        <strong>Performance correcte.</strong> Quelques améliorations possibles.
                    </div>
                @else
                    <div class="alert alert-danger">
                        <i class="feather icon-alert-circle me-2"></i>
                        <strong>Attention !</strong> Les indicateurs nécessitent une amélioration.
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Actions rapides -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-download text-c-blue me-2"></i>Rapports et exports</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-primary btn-block" onclick="exportRapportMensuel()">
                            <i class="feather icon-file-text me-2"></i>Rapport mensuel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success btn-block" onclick="exportRapportAnnuel()">
                            <i class="feather icon-bar-chart me-2"></i>Rapport annuel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-info btn-block" onclick="exportExcel()">
                            <i class="feather icon-download me-2"></i>Export Excel
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning btn-block" onclick="imprimerPage()">
                            <i class="feather icon-printer me-2"></i>Imprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
console.log('=== DÉBUT SCRIPT STATISTIQUES ===');
console.log('Chart.js chargé:', typeof Chart !== 'undefined');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé, début initialisation graphiques...');
    // Données d'évolution
    const evolutionData = @json($evolutionData);

    // Graphique d'évolution - Déclaration globale
    let evolutionChart = null;
    const evolutionCtx = document.getElementById('evolutionChart');
    if (evolutionCtx) {
        evolutionChart = new Chart(evolutionCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: evolutionData.labels,
            datasets: [{
                label: 'Cotisations (€)',
                data: evolutionData.cotisations,
                borderColor: '#1f77b4',
                backgroundColor: 'rgba(31, 119, 180, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Pensions (€)',
                data: evolutionData.pensions,
                borderColor: '#ff7f0e',
                backgroundColor: 'rgba(255, 127, 14, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR', {
                                style: 'currency',
                                currency: 'EUR',
                                minimumFractionDigits: 0
                            }).format(value);
                        }
                    }
                }
            }
        }
    });
    }

    // FONCTION POUR CRÉER LE GRAPHIQUE DE RÉPARTITION
    function createRepartitionChart() {
        console.log('=== CRÉATION GRAPHIQUE RÉPARTITION ===');
        console.log('Chart.js disponible:', typeof Chart !== 'undefined');

        const repartitionCtx = document.getElementById('repartitionChart');
        console.log('Canvas trouvé:', !!repartitionCtx);

        if (!repartitionCtx) {
            console.error('❌ Canvas non trouvé');
            showFallback();
            return;
        }

        if (typeof Chart === 'undefined') {
            console.error('❌ Chart.js non disponible');
            showFallback();
            return;
        }

        try {
            console.log('🎨 Création du graphique...');

            // Données réelles du backend
            const repartitionData = @json($repartitionData);
            console.log('Données backend:', repartitionData);

            const chart = new Chart(repartitionCtx, {
                type: 'doughnut',
                data: {
                    labels: repartitionData.labels || ['Vieillesse', 'Anticipée', 'Invalidité', 'Survivant'],
                    datasets: [{
                        data: repartitionData.data || [4, 2, 0, 0],
                        backgroundColor: ['#1f77b4', '#2ca02c', '#ff7f0e', '#d62728']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: false,
                    plugins: {
                        legend: { display: false }
                    }
                }
            });

            console.log('✅ Graphique créé avec succès !');
            return true;

        } catch (error) {
            console.error('❌ Erreur création graphique:', error);
            showFallback();
            return false;
        }
    }

    // FONCTION POUR AFFICHER LE FALLBACK
    function showFallback() {
        console.log('📊 Affichage du fallback CSS...');

        const canvas = document.getElementById('repartitionChart');
        const fallback = document.getElementById('repartitionFallback');

        if (canvas) canvas.style.display = 'none';
        if (fallback) {
            fallback.style.display = 'block';
            console.log('✅ Fallback affiché');
        }
    }

    // TENTATIVE DE CRÉATION AVEC RETRY
    let attempts = 0;
    const maxAttempts = 5;

    function tryCreateChart() {
        attempts++;
        console.log(`Tentative ${attempts}/${maxAttempts}`);

        if (createRepartitionChart()) {
            console.log('✅ Graphique créé avec succès !');
            return;
        }

        if (attempts < maxAttempts) {
            console.log('⏳ Nouvelle tentative dans 500ms...');
            setTimeout(tryCreateChart, 500);
        } else {
            console.log('❌ Échec après toutes les tentatives - Fallback affiché');
            showFallback();
        }
    }

    // DÉMARRER LA CRÉATION
    tryCreateChart();

    // Gestion des boutons de période
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.btn-group .btn').forEach(b => b.classList.remove('active'));
            // Ajouter la classe active au bouton cliqué
            this.classList.add('active');

            // Récupérer la période
            const period = this.textContent.includes('12') ? 12 :
                          this.textContent.includes('6') ? 6 : 3;

            // Recharger les données (AJAX)
            loadEvolutionData(period);
        });
    });

    // Supprimer le timeout de sécurité si tout s'est bien passé
    clearTimeout(chartTimeout);

    // Fonction pour charger les données d'évolution via AJAX
    function loadEvolutionData(months) {
        if (!evolutionChart) {
            console.error('Graphique d'évolution non initialisé');
            return;
        }

        fetch(`{{ route('statistiques.api.chart-data') }}?type=evolution&period=${months}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Mettre à jour le graphique
                    evolutionChart.data.labels = data.data.labels || [];
                    evolutionChart.data.datasets[0].data = data.data.cotisations || [];
                    evolutionChart.data.datasets[1].data = data.data.pensions || [];
                    evolutionChart.update();
                } else {
                    console.error('Données invalides reçues:', data);
                }
            })
            .catch(error => console.error('Erreur lors du chargement des données:', error));
    }
});

// Fonctions pour les boutons d'export
function exportRapportMensuel() {
    window.location.href = '{{ route("statistiques.export") }}?type=monthly';
}

function exportRapportAnnuel() {
    window.location.href = '{{ route("statistiques.export") }}?type=yearly';
}

function exportExcel() {
    window.location.href = '{{ route("statistiques.export") }}?type=excel';
}

function imprimerPage() {
    window.print();
}
</script>
@endpush
