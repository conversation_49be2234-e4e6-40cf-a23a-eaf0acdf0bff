<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <title>Test Chart.js</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        canvas { border: 1px solid #ddd; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Chart.js</h1>
        
        <div id="status" class="status">Chargement...</div>
        
        <h2>Graphique de test</h2>
        <canvas id="testChart" width="400" height="200"></canvas>
        
        <h2>Données de test</h2>
        <pre id="data"></pre>
    </div>

    <!-- Chart.js -->
    <script src="{{ asset('files/assets/js/chart.min.js') }}"></script>
    
    <script>
        console.log('=== TEST CHART.JS ===');
        
        const statusDiv = document.getElementById('status');
        const dataDiv = document.getElementById('data');
        
        // Test Chart.js
        if (typeof Chart !== 'undefined') {
            statusDiv.textContent = '✅ Chart.js chargé (version: ' + Chart.version + ')';
            statusDiv.className = 'status success';
            
            // Données de test
            const testData = @json($evolutionData ?? ['labels' => ['Jan', 'Feb', 'Mar'], 'cotisations' => [100, 200, 150], 'pensions' => [50, 75, 100]]);
            dataDiv.textContent = JSON.stringify(testData, null, 2);
            
            // Créer le graphique
            const ctx = document.getElementById('testChart');
            if (ctx) {
                try {
                    const chart = new Chart(ctx.getContext('2d'), {
                        type: 'line',
                        data: {
                            labels: testData.labels || ['Jan', 'Feb', 'Mar'],
                            datasets: [{
                                label: 'Cotisations',
                                data: testData.cotisations || [100, 200, 150],
                                borderColor: '#1f77b4',
                                backgroundColor: 'rgba(31, 119, 180, 0.1)',
                                tension: 0.4
                            }, {
                                label: 'Pensions',
                                data: testData.pensions || [50, 75, 100],
                                borderColor: '#ff7f0e',
                                backgroundColor: 'rgba(255, 127, 14, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                    console.log('✅ Graphique créé avec succès');
                } catch (error) {
                    console.error('❌ Erreur création graphique:', error);
                    statusDiv.textContent = '❌ Erreur: ' + error.message;
                    statusDiv.className = 'status error';
                }
            }
        } else {
            statusDiv.textContent = '❌ Chart.js non chargé';
            statusDiv.className = 'status error';
        }
    </script>
</body>
</html>
