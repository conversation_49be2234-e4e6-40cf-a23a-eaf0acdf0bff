@extends('layouts.template')

@section('title', 'Détail Utilisateur - CRFM')

@section('page-header')
@section('page-title', 'Détail de l'utilisateur')
@section('page-description', $user->name . ' - ' . $user->email)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('users.index') }}">Utilisateurs</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">{{ $user->name }}</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Informations principales -->
    <div class="col-md-8">
        <!-- Profil de l'utilisateur -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-user text-c-blue me-2"></i>Profil de l'utilisateur</h5>
                <div class="card-header-right">
                    <div class="btn-group">
                        <a href="{{ route('users.edit', $user) }}" class="btn btn-warning btn-sm">
                            <i class="feather icon-edit me-1"></i>Modifier
                        </a>
                        @if($user->id !== auth()->id())
                        <button class="btn btn-{{ $user->is_active ? 'secondary' : 'success' }} btn-sm" 
                                onclick="toggleStatus()">
                            <i class="feather icon-{{ $user->is_active ? 'pause' : 'play' }} me-1"></i>
                            {{ $user->is_active ? 'Désactiver' : 'Activer' }}
                        </button>
                        <button class="btn btn-info btn-sm" onclick="resetPassword()">
                            <i class="feather icon-key me-1"></i>Réinitialiser mot de passe
                        </button>
                        @endif
                    </div>
                </div>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="user-avatar mb-3">
                            <div class="avatar bg-{{ $user->role_color }} text-white" style="width: 80px; height: 80px; font-size: 2rem;">
                                {{ strtoupper(substr($user->name, 0, 2)) }}
                            </div>
                        </div>
                        <span class="badge bg-{{ $user->role_color }} fs-6">
                            {{ ucfirst($user->role ?? 'operateur') }}
                        </span>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label><strong>Nom complet :</strong></label>
                                    <p>{{ $user->name }}</p>
                                </div>
                                <div class="info-group">
                                    <label><strong>Email :</strong></label>
                                    <p>{{ $user->email }}</p>
                                </div>
                                <div class="info-group">
                                    <label><strong>Téléphone :</strong></label>
                                    <p>{{ $user->telephone ?: 'Non renseigné' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label><strong>Rôle :</strong></label>
                                    <p>
                                        <span class="badge bg-{{ $user->role_color }}">
                                            {{ ucfirst($user->role ?? 'operateur') }}
                                        </span>
                                    </p>
                                </div>
                                <div class="info-group">
                                    <label><strong>Statut :</strong></label>
                                    <p>
                                        <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                            {{ $user->is_active ? 'Actif' : 'Inactif' }}
                                        </span>
                                    </p>
                                </div>
                                <div class="info-group">
                                    <label><strong>Dernière connexion :</strong></label>
                                    <p>{{ $user->last_login_at ? $user->last_login_at->format('d/m/Y à H:i') : 'Jamais connecté' }}</p>
                                </div>
                            </div>
                        </div>
                        @if($user->adresse)
                        <div class="info-group">
                            <label><strong>Adresse :</strong></label>
                            <p>{{ $user->adresse }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Activité récente -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-activity text-c-green me-2"></i>Activité récente</h5>
            </div>
            <div class="card-block">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6>Compte créé</h6>
                            <p class="text-muted">{{ $user->created_at->format('d/m/Y à H:i') }}</p>
                        </div>
                    </div>
                    @if($user->updated_at != $user->created_at)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6>Dernière modification</h6>
                            <p class="text-muted">{{ $user->updated_at->format('d/m/Y à H:i') }}</p>
                        </div>
                    </div>
                    @endif
                    @if($user->last_login_at)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6>Dernière connexion</h6>
                            <p class="text-muted">{{ $user->last_login_at->format('d/m/Y à H:i') }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    <!-- Informations complémentaires -->
    <div class="col-md-4">
        <!-- Statistiques -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bar-chart text-c-blue me-2"></i>Statistiques</h5>
            </div>
            <div class="card-block">
                <div class="stat-item">
                    <div class="d-flex justify-content-between">
                        <span>Compte créé depuis</span>
                        <strong>{{ $user->created_at->diffForHumans() }}</strong>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="d-flex justify-content-between">
                        <span>Dernière activité</span>
                        <strong>{{ $user->updated_at->diffForHumans() }}</strong>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="d-flex justify-content-between">
                        <span>Statut</span>
                        <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                            {{ $user->is_active ? 'Actif' : 'Inactif' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions du rôle -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-shield text-c-green me-2"></i>Permissions</h5>
            </div>
            <div class="card-block">
                @php
                    $permissions = match($user->role ?? 'operateur') {
                        'admin' => [
                            'Gestion complète des utilisateurs',
                            'Configuration du système',
                            'Accès à tous les modules',
                            'Gestion des sauvegardes',
                            'Rapports avancés'
                        ],
                        'gestionnaire' => [
                            'Gestion des adhérents',
                            'Gestion des cotisations',
                            'Gestion des pensions',
                            'Rapports standards',
                            'Consultation des statistiques'
                        ],
                        'operateur' => [
                            'Consultation des adhérents',
                            'Saisie des cotisations',
                            'Consultation des pensions',
                            'Rapports de base'
                        ],
                        default => ['Aucune permission définie']
                    };
                @endphp
                
                <ul class="list-unstyled">
                    @foreach($permissions as $permission)
                    <li class="mb-2">
                        <i class="feather icon-check text-success me-2"></i>
                        {{ $permission }}
                    </li>
                    @endforeach
                </ul>
            </div>
        </div>

        <!-- Actions rapides -->
        @if($user->id !== auth()->id())
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-orange me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="d-grid gap-2">
                    <button class="btn btn-{{ $user->is_active ? 'secondary' : 'success' }}" onclick="toggleStatus()">
                        <i class="feather icon-{{ $user->is_active ? 'pause' : 'play' }} me-2"></i>
                        {{ $user->is_active ? 'Désactiver le compte' : 'Activer le compte' }}
                    </button>
                    <button class="btn btn-info" onclick="resetPassword()">
                        <i class="feather icon-key me-2"></i>Réinitialiser le mot de passe
                    </button>
                    <a href="{{ route('users.edit', $user) }}" class="btn btn-warning">
                        <i class="feather icon-edit me-2"></i>Modifier les informations
                    </a>
                    <button class="btn btn-danger" onclick="deleteUser()">
                        <i class="feather icon-trash me-2"></i>Supprimer le compte
                    </button>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

@if($user->id !== auth()->id())
<!-- Modal de réinitialisation du mot de passe -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Réinitialiser le mot de passe</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="resetPasswordForm" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Réinitialiser le mot de passe de <strong>{{ $user->name }}</strong> ?</p>
                    <div class="form-group">
                        <label class="required">Nouveau mot de passe</label>
                        <input type="password" name="password" class="form-control" required minlength="8">
                        <small class="form-text text-muted">Minimum 8 caractères</small>
                    </div>
                    <div class="form-group">
                        <label class="required">Confirmer le mot de passe</label>
                        <input type="password" name="password_confirmation" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-info">Réinitialiser</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cet utilisateur ?</p>
                <p><strong>{{ $user->name }}</strong> ({{ $user->email }})</p>
                <div class="alert alert-warning">
                    <i class="feather icon-alert-triangle me-2"></i>
                    Cette action est irréversible !
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form method="POST" action="{{ route('users.destroy', $user) }}" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Supprimer définitivement</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
.avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: bold;
    margin: 0 auto;
}
.info-group {
    margin-bottom: 1rem;
}
.info-group label {
    margin-bottom: 0.25rem;
    color: #666;
}
.info-group p {
    margin-bottom: 0;
    color: #333;
}
.stat-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}
.stat-item:last-child {
    border-bottom: none;
}
.timeline {
    position: relative;
    padding-left: 2rem;
}
.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}
.timeline-marker {
    position: absolute;
    left: -1.75rem;
    top: 0.25rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}
.timeline-content h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}
.timeline-content p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
.card-block {
    padding: 1.5rem;
}
</style>
@endpush

@push('scripts')
<script>
function toggleStatus() {
    if (confirm('Changer le statut de cet utilisateur ?')) {
        fetch(`/users/{{ $user->id }}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                CRFM.showToast('Erreur lors du changement de statut', 'error');
            }
        });
    }
}

function resetPassword() {
    const form = document.getElementById('resetPasswordForm');
    form.action = `/users/{{ $user->id }}/reset-password`;

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function deleteUser() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
@endpush
